"""
CopilotKit FastAPI Application

This serves CopilotKit agents and actions through FastAPI with proper configuration,
middleware, error handling, and logging following best practices.
"""

import os
import logging
from dotenv import load_dotenv

# Load environment variables first
load_dotenv()

from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import JSONResponse
import uvicorn

# CopilotKit imports

# Application imports
from copilotkit_config import create_copilotkit_runtime
from actions import get_all_actions
from agents.lead_agent import create_copilotkit_lead_agent
from webintel.webintel_service import router as webintel_router
from fastapi.middleware.cors import CORSMiddleware

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="CopilotKit CRM Backend",
    description="AI-powered CRM backend with CopilotKit integration",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Setup CORS middleware directly
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure as needed
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create CopilotKit runtime
runtime = create_copilotkit_runtime(
    agents=[
        create_copilotkit_lead_agent(
            name="lead_agent",
            description="An AI agent specialized in lead management operations including updating lead information, managing custom fields, and performing bulk operations."
        )
    ],
    actions=get_all_actions()
)

# Setup CopilotKit endpoint with state context
runtime.setup_fastapi_endpoint(app)

# Add other API routes
app.include_router(webintel_router, prefix="/lead", tags=["Lead"])

# Add notes enrich API routes
from api.notes.enrich import router as notes_enrich_router
app.include_router(notes_enrich_router, prefix="/notes", tags=["Notes"])

# Add action execution API routes
from api.actions.execute_task import router as execute_task_router
app.include_router(execute_task_router, prefix="/lead", tags=["Lead"])

# Add Xero integration API routes
from integrations.xero.routers import auth_router, accounting_router
app.include_router(auth_router, prefix="/api", tags=["Xero"])
app.include_router(accounting_router, prefix="/api", tags=["Xero"])

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    runtime_stats = runtime.get_stats()
    return {
        "status": "healthy",
        "service": "CopilotKit CRM Backend",
        "version": "1.0.0",
        "copilotkit": runtime_stats
    }


# Lead agent tools API endpoint
@app.post("/api/lead-agent/tools")
async def call_lead_agent_tool(request: Request):
    """
    Direct API endpoint for calling lead agent tools.

    Expected payload:
    {
        "tool_name": "update_lead_basic_info",
        "args": {
            "leadId": "lead-123",
            "firstName": "John",
            "lastName": "Doe",
            "tenantId": "tenant-123",
            "userId": "user-123"
        }
    }
    """
    try:
        # Import here to avoid circular imports
        from agents.lead_agent.tools import LEAD_AGENT_TOOLS
        from agents.lead_agent.context import set_context, clear_context

        # Parse request body
        body = await request.json()
        tool_name = body.get('tool_name')
        args = body.get('args', {})

        if not tool_name:
            raise HTTPException(status_code=400, detail="tool_name is required")

        # Extract auth context from args
        tenant_id = args.get('tenantId')
        user_id = args.get('userId')

        if not tenant_id or not user_id:
            raise HTTPException(status_code=401, detail="Authentication required")

        # Set context for the request
        set_context(tenant_id, user_id)

        try:
            # Find the tool by name
            tool_function = None
            for tool in LEAD_AGENT_TOOLS:
                if hasattr(tool, 'name') and tool.name == tool_name:
                    tool_function = tool.func
                    break

            if not tool_function:
                raise HTTPException(status_code=404, detail=f"Tool '{tool_name}' not found")

            # Call the tool function
            result = await tool_function(**args)

            return result

        finally:
            # Always clear context after request
            clear_context()

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error calling lead agent tool: {str(e)}")
        clear_context()  # Ensure context is cleared on error
        raise HTTPException(status_code=500, detail=str(e))


# CoAgent endpoint for frontend useCoAgent integration
@app.post("/api/co-agent/lead")
async def handle_co_agent_lead_request(request: Request):
    """
    CoAgent endpoint for handling frontend useCoAgent requests.

    Expected payload:
    {
        "action": "updateLeadBasicInfo",
        "args": {...},
        "state": {
            "tenantId": "...",
            "userId": "...",
            "leadId": "..."
        }
    }
    """
    try:
        from agents.lead_agent.co_agent import handle_co_agent_request

        # Parse request body
        body = await request.json()

        # Handle the CoAgent request
        result = await handle_co_agent_request(body)

        return result

    except Exception as e:
        logger.error(f"Error handling CoAgent request: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Error handlers
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler."""
    logger.error(f"Unhandled exception: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={"error": "Internal server error", "detail": str(exc)}
    )

def main():
    """Run the uvicorn server."""
    port = int(os.getenv("PORT", "8000"))
    host = os.getenv("HOST", "0.0.0.0")
    reload = os.getenv("RELOAD", "true").lower() == "true"

    logger.info(f"Starting CopilotKit CRM Backend on {host}:{port}")
    logger.info(f"CopilotKit endpoint: http://{host}:{port}/copilotkit")
    logger.info(f"API documentation: http://{host}:{port}/docs")

    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=reload,
        log_level="info"
    )

if __name__ == "__main__":
    main()
