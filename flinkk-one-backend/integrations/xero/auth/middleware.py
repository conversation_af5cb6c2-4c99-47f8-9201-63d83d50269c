"""
Xero Authentication Middleware

FastAPI middleware and dependencies for Xero authentication
and token management.
"""

import logging
from typing import Optional, Annotated
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from ..client import XeroClient
from ..exceptions import XeroTokenError, XeroAuthenticationError
from xero_python.api_client.oauth2 import OAuth2Token

logger = logging.getLogger(__name__)

# Security scheme for Bearer token
security = HTTPBearer(auto_error=False)


class XeroAuthMiddleware:
    """
    Middleware for handling Xero authentication and token management.
    """
    
    def __init__(self):
        """Initialize middleware."""
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def get_xero_token_from_session(self, request: Request) -> Optional[OAuth2Token]:
        """
        Get Xero token from session.
        
        Args:
            request: FastAPI request object
            
        Returns:
            OAuth2Token if found in session, None otherwise
        """
        try:
            # Check if session exists and has token
            if hasattr(request, 'session') and 'xero_token' in request.session:
                token_data = request.session['xero_token']
                
                # Convert session data back to OAuth2Token
                if isinstance(token_data, dict):
                    from ..config import xero_config
                    return OAuth2Token(
                        client_id=xero_config.client_id,
                        client_secret=xero_config.client_secret,
                        **token_data
                    )
                
                return token_data
        except Exception as e:
            self.logger.warning(f"Failed to get token from session: {str(e)}")
        
        return None
    
    async def get_xero_token_from_header(
        self, 
        credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
    ) -> Optional[OAuth2Token]:
        """
        Get Xero token from Authorization header.
        
        Args:
            credentials: HTTP authorization credentials
            
        Returns:
            OAuth2Token if valid token found, None otherwise
        """
        if not credentials:
            return None
        
        try:
            # For now, we'll assume the token is passed directly
            # In production, you might want to validate against a database
            from ..config import xero_config
            
            return OAuth2Token(
                client_id=xero_config.client_id,
                client_secret=xero_config.client_secret,
                access_token=credentials.credentials
            )
        except Exception as e:
            self.logger.warning(f"Failed to parse token from header: {str(e)}")
        
        return None
    
    async def store_token_in_session(self, request: Request, token: OAuth2Token) -> None:
        """
        Store Xero token in session.
        
        Args:
            request: FastAPI request object
            token: OAuth2Token to store
        """
        try:
            if hasattr(request, 'session'):
                # Convert token to serializable format
                token_data = {
                    'access_token': token.access_token,
                    'refresh_token': getattr(token, 'refresh_token', None),
                    'expires_at': getattr(token, 'expires_at', None),
                    'scope': getattr(token, 'scope', None),
                    'token_type': getattr(token, 'token_type', 'Bearer')
                }
                request.session['xero_token'] = token_data
                self.logger.info("Xero token stored in session")
        except Exception as e:
            self.logger.error(f"Failed to store token in session: {str(e)}")


# Global middleware instance
xero_auth_middleware = XeroAuthMiddleware()


async def get_optional_xero_client(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[XeroClient]:
    """
    Dependency to get optional Xero client.
    
    Args:
        request: FastAPI request object
        credentials: HTTP authorization credentials
        
    Returns:
        XeroClient if authenticated, None otherwise
    """
    # Try to get token from session first, then from header
    token = await xero_auth_middleware.get_xero_token_from_session(request)
    if not token:
        token = await xero_auth_middleware.get_xero_token_from_header(credentials)
    
    if token:
        return XeroClient(oauth2_token=token)
    
    return None


async def get_required_xero_client(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> XeroClient:
    """
    Dependency to get required Xero client.
    
    Args:
        request: FastAPI request object
        credentials: HTTP authorization credentials
        
    Returns:
        XeroClient if authenticated
        
    Raises:
        HTTPException: If not authenticated
    """
    client = await get_optional_xero_client(request, credentials)
    
    if not client:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Xero authentication required",
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    return client


async def get_xero_tenant_id(
    xero_client: XeroClient = Depends(get_required_xero_client)
) -> str:
    """
    Dependency to get Xero tenant ID.
    
    Args:
        xero_client: Authenticated Xero client
        
    Returns:
        Xero tenant ID
        
    Raises:
        HTTPException: If tenant ID cannot be retrieved
    """
    try:
        tenant_id = xero_client.get_tenant_id()
        if not tenant_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Unable to retrieve Xero tenant ID"
            )
        return tenant_id
    except Exception as e:
        logger.error(f"Failed to get tenant ID: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve tenant information"
        )


# Type aliases for dependency injection
OptionalXeroClient = Annotated[Optional[XeroClient], Depends(get_optional_xero_client)]
RequiredXeroClient = Annotated[XeroClient, Depends(get_required_xero_client)]
XeroTenantId = Annotated[str, Depends(get_xero_tenant_id)]
