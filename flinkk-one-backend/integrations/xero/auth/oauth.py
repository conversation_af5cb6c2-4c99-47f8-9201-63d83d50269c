"""
Xero OAuth2 Manager

Handles the OAuth2 authentication flow for Xero integration
including authorization URL generation, token exchange, and refresh.
"""

import secrets
import logging
from typing import Optional, Dict, Any, Tuple
from urllib.parse import urlencode
from authlib.integrations.httpx_client import AsyncOAuth2<PERSON>lient
from xero_python.api_client.oauth2 import OAuth2Token

from ..config import xero_config
from ..exceptions import XeroAuthenticationError, XeroTokenError

logger = logging.getLogger(__name__)


class XeroOAuth2Manager:
    """
    Manages Xero OAuth2 authentication flow.
    """
    
    def __init__(self):
        """Initialize OAuth2 manager."""
        self.client_id = xero_config.client_id
        self.client_secret = xero_config.client_secret
        self.redirect_uri = xero_config.redirect_uri
        self.authorization_url = xero_config.authorization_url
        self.token_url = xero_config.token_url
        self.scopes = xero_config.scope_string
    
    def generate_authorization_url(self, state: Optional[str] = None) -> Tuple[str, str]:
        """
        Generate authorization URL for OAuth2 flow.
        
        Args:
            state: Optional state parameter for security
            
        Returns:
            Tuple of (authorization_url, state)
        """
        if state is None:
            state = secrets.token_urlsafe(32)
        
        params = {
            "response_type": "code",
            "client_id": self.client_id,
            "redirect_uri": self.redirect_uri,
            "scope": self.scopes,
            "state": state,
        }
        
        auth_url = f"{self.authorization_url}?{urlencode(params)}"
        
        logger.info(f"Generated Xero authorization URL with state: {state}")
        return auth_url, state
    
    async def exchange_code_for_token(
        self,
        authorization_code: str,
        state: Optional[str] = None
    ) -> OAuth2Token:
        """
        Exchange authorization code for access token.
        
        Args:
            authorization_code: Authorization code from callback
            state: State parameter for validation
            
        Returns:
            OAuth2Token with access and refresh tokens
            
        Raises:
            XeroAuthenticationError: If token exchange fails
        """
        try:
            async with AsyncOAuth2Client(
                client_id=self.client_id,
                client_secret=self.client_secret,
                redirect_uri=self.redirect_uri
            ) as client:
                
                token_data = await client.fetch_token(
                    url=self.token_url,
                    code=authorization_code,
                    redirect_uri=self.redirect_uri
                )
                
                # Create OAuth2Token instance
                oauth2_token = OAuth2Token(
                    client_id=self.client_id,
                    client_secret=self.client_secret,
                    access_token=token_data.get("access_token"),
                    refresh_token=token_data.get("refresh_token"),
                    expires_at=token_data.get("expires_at"),
                    scope=token_data.get("scope", self.scopes)
                )
                
                logger.info("Successfully exchanged authorization code for Xero token")
                return oauth2_token
                
        except Exception as e:
            logger.error(f"Failed to exchange code for token: {str(e)}")
            raise XeroAuthenticationError(f"Token exchange failed: {str(e)}")
    
    async def refresh_access_token(self, refresh_token: str) -> OAuth2Token:
        """
        Refresh access token using refresh token.
        
        Args:
            refresh_token: Refresh token
            
        Returns:
            New OAuth2Token with refreshed access token
            
        Raises:
            XeroTokenError: If token refresh fails
        """
        try:
            async with AsyncOAuth2Client(
                client_id=self.client_id,
                client_secret=self.client_secret
            ) as client:
                
                token_data = await client.refresh_token(
                    url=self.token_url,
                    refresh_token=refresh_token
                )
                
                # Create new OAuth2Token instance
                oauth2_token = OAuth2Token(
                    client_id=self.client_id,
                    client_secret=self.client_secret,
                    access_token=token_data.get("access_token"),
                    refresh_token=token_data.get("refresh_token", refresh_token),
                    expires_at=token_data.get("expires_at"),
                    scope=token_data.get("scope", self.scopes)
                )
                
                logger.info("Successfully refreshed Xero access token")
                return oauth2_token
                
        except Exception as e:
            logger.error(f"Failed to refresh token: {str(e)}")
            raise XeroTokenError(f"Token refresh failed: {str(e)}")
    
    def validate_state(self, received_state: str, expected_state: str) -> bool:
        """
        Validate OAuth2 state parameter.
        
        Args:
            received_state: State received from callback
            expected_state: Expected state value
            
        Returns:
            True if state is valid, False otherwise
        """
        return secrets.compare_digest(received_state, expected_state)
    
    def is_token_expired(self, token: OAuth2Token) -> bool:
        """
        Check if token is expired.
        
        Args:
            token: OAuth2Token to check
            
        Returns:
            True if token is expired, False otherwise
        """
        if not hasattr(token, 'expires_at') or token.expires_at is None:
            return False
        
        import time
        return time.time() >= token.expires_at
