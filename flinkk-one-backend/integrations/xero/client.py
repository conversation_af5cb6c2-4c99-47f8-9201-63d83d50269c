"""
Xero API Client

Provides a centralized client for interacting with Xero APIs
with proper configuration, error handling, and async support.
"""

import logging
from typing import Optional, Dict, Any
from xero_python.api_client import ApiClient
from xero_python.api_client.configuration import Configuration
from xero_python.api_client.oauth2 import OAuth2Token
from xero_python.accounting import AccountingApi
from xero_python.identity import Identity<PERSON><PERSON>

from .config import xero_config
from .exceptions import XeroConfigurationError, XeroTokenError

logger = logging.getLogger(__name__)


class XeroClient:
    """
    Centralized Xero API client with proper configuration and token management.
    """
    
    def __init__(self, oauth2_token: Optional[OAuth2Token] = None):
        """
        Initialize Xero client.
        
        Args:
            oauth2_token: OAuth2 token for authenticated requests
        """
        self._oauth2_token = oauth2_token
        self._api_client: Optional[ApiClient] = None
        self._accounting_api: Optional[AccountingApi] = None
        self._identity_api: Optional[IdentityApi] = None
        
        # Validate configuration
        self._validate_config()
    
    def _validate_config(self) -> None:
        """Validate Xero configuration."""
        if not xero_config.client_id:
            raise XeroConfigurationError("XERO_CLIENT_ID is required")
        if not xero_config.client_secret:
            raise XeroConfigurationError("XERO_CLIENT_SECRET is required")
        if not xero_config.redirect_uri:
            raise XeroConfigurationError("XERO_REDIRECT_URI is required")
    
    @property
    def api_client(self) -> ApiClient:
        """Get or create the API client."""
        if self._api_client is None:
            configuration = Configuration(
                debug=xero_config.debug,
                oauth2_token=self._oauth2_token or OAuth2Token(
                    client_id=xero_config.client_id,
                    client_secret=xero_config.client_secret
                )
            )
            
            self._api_client = ApiClient(
                configuration=configuration,
                pool_threads=1
            )
            
            # Set token getter and saver if token is provided
            if self._oauth2_token:
                self._api_client.oauth2_token_getter = lambda: self._oauth2_token
                self._api_client.oauth2_token_saver = self._update_token
        
        return self._api_client
    
    @property
    def accounting_api(self) -> AccountingApi:
        """Get or create the Accounting API instance."""
        if self._accounting_api is None:
            self._accounting_api = AccountingApi(self.api_client)
        return self._accounting_api
    
    @property
    def identity_api(self) -> IdentityApi:
        """Get or create the Identity API instance."""
        if self._identity_api is None:
            self._identity_api = IdentityApi(self.api_client)
        return self._identity_api
    
    def set_token(self, token: OAuth2Token) -> None:
        """
        Set the OAuth2 token for the client.
        
        Args:
            token: OAuth2 token to set
        """
        self._oauth2_token = token
        
        # Reset API instances to use new token
        self._api_client = None
        self._accounting_api = None
        self._identity_api = None
    
    def _update_token(self, token: Dict[str, Any]) -> None:
        """
        Update the OAuth2 token.
        
        Args:
            token: Updated token data
        """
        if isinstance(token, dict):
            # Convert dict to OAuth2Token if needed
            self._oauth2_token = OAuth2Token(
                client_id=xero_config.client_id,
                client_secret=xero_config.client_secret,
                **token
            )
        else:
            self._oauth2_token = token
        
        logger.info("Xero OAuth2 token updated")
    
    def is_authenticated(self) -> bool:
        """Check if the client has a valid token."""
        return self._oauth2_token is not None and hasattr(self._oauth2_token, 'access_token')
    
    def refresh_token(self) -> OAuth2Token:
        """
        Refresh the OAuth2 token.
        
        Returns:
            Refreshed OAuth2 token
            
        Raises:
            XeroTokenError: If token refresh fails
        """
        if not self._oauth2_token:
            raise XeroTokenError("No token available to refresh")
        
        try:
            new_token = self.api_client.refresh_oauth2_token()
            self.set_token(new_token)
            logger.info("Xero OAuth2 token refreshed successfully")
            return new_token
        except Exception as e:
            logger.error(f"Failed to refresh Xero token: {str(e)}")
            raise XeroTokenError(f"Token refresh failed: {str(e)}")
    
    def get_tenant_id(self) -> Optional[str]:
        """
        Get the first organization tenant ID.
        
        Returns:
            Tenant ID if available, None otherwise
        """
        if not self.is_authenticated():
            return None
        
        try:
            connections = self.identity_api.get_connections()
            for connection in connections:
                if connection.tenant_type == "ORGANISATION":
                    return connection.tenant_id
        except Exception as e:
            logger.error(f"Failed to get tenant ID: {str(e)}")
        
        return None
