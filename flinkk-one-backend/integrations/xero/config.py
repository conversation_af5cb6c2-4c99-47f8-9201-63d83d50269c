"""
Xero Configuration Management

Handles Xero OAuth2 configuration, environment variables, and settings
for the Xero integration.
"""

import os
from typing import Optional, List
from pydantic import BaseSettings, Field, validator


class XeroConfig(BaseSettings):
    """Xero integration configuration."""
    
    # OAuth2 Configuration
    client_id: str = Field(..., env="XERO_CLIENT_ID")
    client_secret: str = Field(..., env="XERO_CLIENT_SECRET")
    redirect_uri: str = Field(..., env="XERO_REDIRECT_URI")
    
    # Xero API Configuration
    base_url: str = Field(default="https://api.xero.com", env="XERO_BASE_URL")
    authorization_url: str = Field(
        default="https://login.xero.com/identity/connect/authorize",
        env="XERO_AUTHORIZATION_URL"
    )
    token_url: str = Field(
        default="https://identity.xero.com/connect/token",
        env="XERO_TOKEN_URL"
    )
    
    # OAuth2 Scopes
    scopes: List[str] = Field(
        default=[
            "offline_access",
            "openid",
            "profile",
            "email",
            "accounting.transactions",
            "accounting.contacts",
            "accounting.settings",
            "accounting.reports.read",
            "accounting.journals.read",
            "accounting.attachments",
            "files",
            "assets",
            "projects"
        ],
        env="XERO_SCOPES"
    )
    
    # Security Configuration
    state_secret: str = Field(..., env="XERO_STATE_SECRET")
    token_encryption_key: Optional[str] = Field(None, env="XERO_TOKEN_ENCRYPTION_KEY")
    
    # Application Configuration
    debug: bool = Field(default=False, env="XERO_DEBUG")
    timeout: int = Field(default=30, env="XERO_TIMEOUT")
    max_retries: int = Field(default=3, env="XERO_MAX_RETRIES")
    
    # Database Configuration
    token_table_name: str = Field(default="xero_tokens", env="XERO_TOKEN_TABLE")
    
    @validator("scopes", pre=True)
    def parse_scopes(cls, v):
        """Parse scopes from string or list."""
        if isinstance(v, str):
            return [scope.strip() for scope in v.split(",")]
        return v
    
    @property
    def scope_string(self) -> str:
        """Get scopes as a space-separated string."""
        return " ".join(self.scopes)
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# Global configuration instance
xero_config = XeroConfig()
