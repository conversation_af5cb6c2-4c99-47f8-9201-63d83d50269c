"""
Xero Authentication Router

FastAPI router for Xero OAuth2 authentication endpoints
including authorization, callback, and token management.
"""

import logging
from typing import Optional
from fastapi import APIRouter, Request, HTTPException, status, Depends, Query
from fastapi.responses import RedirectResponse, JSONResponse

from ..auth.oauth import XeroOAuth2Manager
from ..auth.middleware import xero_auth_middleware, OptionalXeroClient
from ..models.auth import XeroAuthCallback, XeroTokenResponse, XeroConnectionInfo
from ..exceptions import XeroAuthenticationError, XeroTokenError
from ..config import xero_config

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/xero/auth", tags=["Xero Authentication"])

# OAuth2 manager instance
oauth_manager = XeroOAuth2Manager()


@router.get("/authorize")
async def authorize(
    request: Request,
    redirect_url: Optional[str] = Query(None, description="URL to redirect after auth")
) -> RedirectResponse:
    """
    Initiate Xero OAuth2 authorization flow.
    
    Args:
        request: FastAPI request object
        redirect_url: Optional URL to redirect to after successful authentication
        
    Returns:
        Redirect to Xero authorization URL
    """
    try:
        # Generate authorization URL with state
        auth_url, state = oauth_manager.generate_authorization_url()
        
        # Store state and redirect URL in session for validation
        if hasattr(request, 'session'):
            request.session['xero_oauth_state'] = state
            if redirect_url:
                request.session['xero_redirect_url'] = redirect_url
        
        logger.info(f"Redirecting to Xero authorization URL with state: {state}")
        return RedirectResponse(url=auth_url, status_code=status.HTTP_302_FOUND)
        
    except Exception as e:
        logger.error(f"Failed to initiate authorization: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to initiate Xero authorization"
        )


@router.get("/callback")
async def oauth_callback(
    request: Request,
    code: str = Query(..., description="Authorization code"),
    state: str = Query(..., description="OAuth2 state parameter"),
    scope: Optional[str] = Query(None, description="Granted scope"),
    error: Optional[str] = Query(None, description="Error code"),
    error_description: Optional[str] = Query(None, description="Error description")
) -> RedirectResponse:
    """
    Handle Xero OAuth2 callback.
    
    Args:
        request: FastAPI request object
        code: Authorization code from Xero
        state: OAuth2 state parameter
        scope: Granted scope
        error: Error code if authorization failed
        error_description: Error description if authorization failed
        
    Returns:
        Redirect response based on success/failure
    """
    try:
        # Check for authorization errors
        if error:
            logger.error(f"Xero authorization error: {error} - {error_description}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Authorization failed: {error_description or error}"
            )
        
        # Validate state parameter
        expected_state = None
        if hasattr(request, 'session'):
            expected_state = request.session.get('xero_oauth_state')
        
        if not expected_state or not oauth_manager.validate_state(state, expected_state):
            logger.error("Invalid OAuth2 state parameter")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid state parameter"
            )
        
        # Exchange code for token
        token = await oauth_manager.exchange_code_for_token(code, state)
        
        # Store token in session
        await xero_auth_middleware.store_token_in_session(request, token)
        
        # Get redirect URL from session or use default
        redirect_url = "/"
        if hasattr(request, 'session'):
            redirect_url = request.session.pop('xero_redirect_url', redirect_url)
            # Clean up session
            request.session.pop('xero_oauth_state', None)
        
        logger.info("Xero OAuth2 callback processed successfully")
        return RedirectResponse(url=redirect_url, status_code=status.HTTP_302_FOUND)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"OAuth callback error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process OAuth callback"
        )


@router.post("/refresh")
async def refresh_token(
    request: Request,
    xero_client: OptionalXeroClient
) -> XeroTokenResponse:
    """
    Refresh Xero access token.
    
    Args:
        request: FastAPI request object
        xero_client: Optional Xero client
        
    Returns:
        Token response with new token data
    """
    try:
        if not xero_client:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="No Xero token found to refresh"
            )
        
        # Refresh the token
        new_token = xero_client.refresh_token()
        
        # Store new token in session
        await xero_auth_middleware.store_token_in_session(request, new_token)
        
        # Get tenant ID for response
        tenant_id = xero_client.get_tenant_id()
        
        return XeroTokenResponse(
            success=True,
            token_data={
                "access_token": new_token.access_token,
                "refresh_token": getattr(new_token, 'refresh_token', None),
                "expires_at": getattr(new_token, 'expires_at', None),
                "scope": getattr(new_token, 'scope', None),
                "token_type": getattr(new_token, 'token_type', 'Bearer')
            },
            tenant_id=tenant_id
        )
        
    except XeroTokenError as e:
        logger.error(f"Token refresh failed: {str(e)}")
        return XeroTokenResponse(
            success=False,
            error=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error during token refresh: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to refresh token"
        )


@router.get("/connections")
async def get_connections(xero_client: OptionalXeroClient) -> list[XeroConnectionInfo]:
    """
    Get Xero connections for the authenticated user.
    
    Args:
        xero_client: Optional Xero client
        
    Returns:
        List of Xero connections
    """
    try:
        if not xero_client:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Xero authentication required"
            )
        
        connections = xero_client.identity_api.get_connections()
        
        return [
            XeroConnectionInfo(
                id=conn.id,
                tenant_id=conn.tenant_id,
                tenant_type=conn.tenant_type,
                tenant_name=getattr(conn, 'tenant_name', None),
                created_date_utc=getattr(conn, 'created_date_utc', None),
                updated_date_utc=getattr(conn, 'updated_date_utc', None)
            )
            for conn in connections
        ]
        
    except Exception as e:
        logger.error(f"Failed to get connections: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve connections"
        )


@router.post("/logout")
async def logout(request: Request) -> JSONResponse:
    """
    Logout and clear Xero session.
    
    Args:
        request: FastAPI request object
        
    Returns:
        Success response
    """
    try:
        if hasattr(request, 'session'):
            # Clear Xero-related session data
            request.session.pop('xero_token', None)
            request.session.pop('xero_oauth_state', None)
            request.session.pop('xero_redirect_url', None)
        
        logger.info("Xero session cleared")
        return JSONResponse(
            content={"success": True, "message": "Logged out successfully"},
            status_code=status.HTTP_200_OK
        )
        
    except Exception as e:
        logger.error(f"Logout error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to logout"
        )


@router.get("/status")
async def auth_status(xero_client: OptionalXeroClient) -> dict:
    """
    Get current authentication status.
    
    Args:
        xero_client: Optional Xero client
        
    Returns:
        Authentication status information
    """
    if not xero_client:
        return {
            "authenticated": False,
            "tenant_id": None,
            "connections": []
        }
    
    try:
        tenant_id = xero_client.get_tenant_id()
        connections = xero_client.identity_api.get_connections()
        
        return {
            "authenticated": True,
            "tenant_id": tenant_id,
            "connections": len(connections),
            "client_configured": True
        }
        
    except Exception as e:
        logger.error(f"Failed to get auth status: {str(e)}")
        return {
            "authenticated": False,
            "tenant_id": None,
            "connections": 0,
            "error": str(e)
        }
