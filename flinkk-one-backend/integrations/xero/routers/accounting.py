"""
Xero Accounting Router

FastAPI router for Xero Accounting API endpoints
including organizations, invoices, contacts, and other accounting data.
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime
from fastapi import APIRouter, HTTPException, status, Query, Path, Body

from ..auth.middleware import RequiredXeroClient, XeroTenantId
from ..services.accounting import XeroAccountingService
from ..services.contacts import XeroContactsService
from ..models.accounting import (
    XeroOrganization, XeroInvoice, XeroContact, 
    InvoiceStatus, ContactStatus
)
from ..exceptions import XeroAPIError

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/xero/accounting", tags=["Xero Accounting"])


@router.get("/organizations", response_model=List[XeroOrganization])
async def get_organizations(
    xero_client: RequiredXeroClient,
    tenant_id: XeroTenantId
) -> List[XeroOrganization]:
    """
    Get organizations for the authenticated tenant.
    
    Args:
        xero_client: Authenticated Xero client
        tenant_id: Xero tenant ID
        
    Returns:
        List of organizations
    """
    try:
        service = XeroAccountingService(xero_client)
        return await service.get_organizations(tenant_id)
    except XeroAPIError as e:
        logger.error(f"Failed to get organizations: {str(e)}")
        raise HTTPException(
            status_code=e.status_code or status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error getting organizations: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve organizations"
        )


@router.get("/invoices", response_model=List[XeroInvoice])
async def get_invoices(
    xero_client: RequiredXeroClient,
    tenant_id: XeroTenantId,
    statuses: Optional[List[InvoiceStatus]] = Query(None, description="Filter by invoice statuses"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: Optional[int] = Query(None, ge=1, le=100, description="Maximum results per page"),
    modified_since: Optional[datetime] = Query(None, description="Only return invoices modified since this date")
) -> List[XeroInvoice]:
    """
    Get invoices with optional filtering and pagination.
    
    Args:
        xero_client: Authenticated Xero client
        tenant_id: Xero tenant ID
        statuses: Filter by invoice statuses
        page: Page number for pagination
        limit: Maximum results per page
        modified_since: Only return invoices modified since this date
        
    Returns:
        List of invoices
    """
    try:
        service = XeroAccountingService(xero_client)
        return await service.get_invoices(
            tenant_id=tenant_id,
            statuses=statuses,
            page=page,
            limit=limit,
            modified_since=modified_since
        )
    except XeroAPIError as e:
        logger.error(f"Failed to get invoices: {str(e)}")
        raise HTTPException(
            status_code=e.status_code or status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error getting invoices: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve invoices"
        )


@router.get("/invoices/{invoice_id}", response_model=XeroInvoice)
async def get_invoice(
    invoice_id: str = Path(..., description="Invoice ID"),
    xero_client: RequiredXeroClient = None,
    tenant_id: XeroTenantId = None
) -> XeroInvoice:
    """
    Get a specific invoice by ID.
    
    Args:
        invoice_id: Invoice ID to retrieve
        xero_client: Authenticated Xero client
        tenant_id: Xero tenant ID
        
    Returns:
        Invoice details
    """
    try:
        service = XeroAccountingService(xero_client)
        invoice = await service.get_invoice_by_id(invoice_id, tenant_id)
        
        if not invoice:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Invoice {invoice_id} not found"
            )
        
        return invoice
    except HTTPException:
        raise
    except XeroAPIError as e:
        logger.error(f"Failed to get invoice {invoice_id}: {str(e)}")
        raise HTTPException(
            status_code=e.status_code or status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error getting invoice {invoice_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve invoice"
        )


@router.get("/contacts", response_model=List[XeroContact])
async def get_contacts(
    xero_client: RequiredXeroClient,
    tenant_id: XeroTenantId,
    page: int = Query(1, ge=1, description="Page number"),
    limit: Optional[int] = Query(None, ge=1, le=100, description="Maximum results per page"),
    search: Optional[str] = Query(None, description="Search term for contact name or email"),
    include_archived: bool = Query(False, description="Include archived contacts"),
    modified_since: Optional[datetime] = Query(None, description="Only return contacts modified since this date")
) -> List[XeroContact]:
    """
    Get contacts with optional filtering and pagination.
    
    Args:
        xero_client: Authenticated Xero client
        tenant_id: Xero tenant ID
        page: Page number for pagination
        limit: Maximum results per page
        search: Search term for contact name or email
        include_archived: Include archived contacts
        modified_since: Only return contacts modified since this date
        
    Returns:
        List of contacts
    """
    try:
        service = XeroContactsService(xero_client)
        
        if search:
            return await service.search_contacts(
                search_term=search,
                tenant_id=tenant_id,
                limit=limit or 50
            )
        else:
            return await service.get_contacts(
                tenant_id=tenant_id,
                page=page,
                limit=limit,
                modified_since=modified_since,
                include_archived=include_archived
            )
    except XeroAPIError as e:
        logger.error(f"Failed to get contacts: {str(e)}")
        raise HTTPException(
            status_code=e.status_code or status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error getting contacts: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve contacts"
        )


@router.get("/contacts/{contact_id}", response_model=XeroContact)
async def get_contact(
    contact_id: str = Path(..., description="Contact ID"),
    xero_client: RequiredXeroClient = None,
    tenant_id: XeroTenantId = None
) -> XeroContact:
    """
    Get a specific contact by ID.
    
    Args:
        contact_id: Contact ID to retrieve
        xero_client: Authenticated Xero client
        tenant_id: Xero tenant ID
        
    Returns:
        Contact details
    """
    try:
        service = XeroContactsService(xero_client)
        contact = await service.get_contact_by_id(contact_id, tenant_id)
        
        if not contact:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Contact {contact_id} not found"
            )
        
        return contact
    except HTTPException:
        raise
    except XeroAPIError as e:
        logger.error(f"Failed to get contact {contact_id}: {str(e)}")
        raise HTTPException(
            status_code=e.status_code or status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error getting contact {contact_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve contact"
        )


@router.post("/contacts", response_model=XeroContact)
async def create_contact(
    contact_data: Dict[str, Any] = Body(..., description="Contact data"),
    xero_client: RequiredXeroClient = None,
    tenant_id: XeroTenantId = None
) -> XeroContact:
    """
    Create a new contact.
    
    Args:
        contact_data: Contact data
        xero_client: Authenticated Xero client
        tenant_id: Xero tenant ID
        
    Returns:
        Created contact
    """
    try:
        service = XeroContactsService(xero_client)
        return await service.create_contact(contact_data, tenant_id)
    except XeroAPIError as e:
        logger.error(f"Failed to create contact: {str(e)}")
        raise HTTPException(
            status_code=e.status_code or status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error creating contact: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create contact"
        )


@router.put("/contacts/{contact_id}", response_model=XeroContact)
async def update_contact(
    contact_id: str = Path(..., description="Contact ID"),
    contact_data: Dict[str, Any] = Body(..., description="Updated contact data"),
    xero_client: RequiredXeroClient = None,
    tenant_id: XeroTenantId = None
) -> XeroContact:
    """
    Update an existing contact.
    
    Args:
        contact_id: Contact ID to update
        contact_data: Updated contact data
        xero_client: Authenticated Xero client
        tenant_id: Xero tenant ID
        
    Returns:
        Updated contact
    """
    try:
        service = XeroContactsService(xero_client)
        return await service.update_contact(contact_id, contact_data, tenant_id)
    except XeroAPIError as e:
        logger.error(f"Failed to update contact {contact_id}: {str(e)}")
        raise HTTPException(
            status_code=e.status_code or status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error updating contact {contact_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update contact"
        )


@router.get("/accounts")
async def get_accounts(
    xero_client: RequiredXeroClient,
    tenant_id: XeroTenantId
) -> List[Dict[str, Any]]:
    """
    Get chart of accounts.
    
    Args:
        xero_client: Authenticated Xero client
        tenant_id: Xero tenant ID
        
    Returns:
        List of accounts
    """
    try:
        service = XeroAccountingService(xero_client)
        return await service.get_accounts(tenant_id)
    except XeroAPIError as e:
        logger.error(f"Failed to get accounts: {str(e)}")
        raise HTTPException(
            status_code=e.status_code or status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error getting accounts: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve accounts"
        )


@router.get("/tracking-categories")
async def get_tracking_categories(
    xero_client: RequiredXeroClient,
    tenant_id: XeroTenantId
) -> List[Dict[str, Any]]:
    """
    Get tracking categories.
    
    Args:
        xero_client: Authenticated Xero client
        tenant_id: Xero tenant ID
        
    Returns:
        List of tracking categories
    """
    try:
        service = XeroAccountingService(xero_client)
        return await service.get_tracking_categories(tenant_id)
    except XeroAPIError as e:
        logger.error(f"Failed to get tracking categories: {str(e)}")
        raise HTTPException(
            status_code=e.status_code or status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error getting tracking categories: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve tracking categories"
        )


@router.get("/health")
async def health_check(
    xero_client: RequiredXeroClient,
    tenant_id: XeroTenantId
) -> Dict[str, Any]:
    """
    Perform health check for Xero accounting services.
    
    Args:
        xero_client: Authenticated Xero client
        tenant_id: Xero tenant ID
        
    Returns:
        Health check results
    """
    try:
        accounting_service = XeroAccountingService(xero_client)
        contacts_service = XeroContactsService(xero_client)
        
        accounting_health = await accounting_service.health_check()
        contacts_health = await contacts_service.health_check()
        
        return {
            "overall_status": "healthy" if all([
                accounting_health.get("status") == "healthy",
                contacts_health.get("status") == "healthy"
            ]) else "unhealthy",
            "services": {
                "accounting": accounting_health,
                "contacts": contacts_health
            },
            "tenant_id": tenant_id
        }
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return {
            "overall_status": "unhealthy",
            "error": str(e),
            "tenant_id": tenant_id
        }
