# Xero Integration for FastAPI

A comprehensive, production-ready Xero integration for the flinkk-one-backend FastAPI application.

## Overview

This integration provides a clean, maintainable interface to Xero's APIs with proper authentication, error handling, and security measures. It follows FastAPI best practices and provides both synchronous and asynchronous support.

## Features

- **OAuth2 Authentication Flow**: Complete implementation of Xero's OAuth2 flow
- **Accounting API Integration**: Access to contacts, invoices, organizations, and more
- **Identity API Integration**: User and tenant management
- **Security**: Token encryption, state validation, and request security
- **Error Handling**: Comprehensive error handling with custom exceptions
- **Async Support**: Full async/await support for better performance
- **Type Safety**: Pydantic models for all data structures
- **Logging**: Detailed logging for debugging and monitoring

## Quick Start

### 1. Environment Configuration

Copy the environment template and configure your Xero app credentials:

```bash
cp .env.example .env
```

Update the following variables in your `.env` file:

```env
# Xero OAuth2 Configuration
XERO_CLIENT_ID=your_xero_client_id_here
XERO_CLIENT_SECRET=your_xero_client_secret_here
XERO_REDIRECT_URI=http://localhost:8000/api/xero/auth/callback

# Security Configuration
XERO_STATE_SECRET=your_random_secret_for_oauth_state_generation
XERO_TOKEN_ENCRYPTION_KEY=your_32_character_encryption_key_here
```

### 2. Xero App Setup

1. Create a Xero app at https://developer.xero.com/
2. Configure the redirect URI to match your `XERO_REDIRECT_URI`
3. Copy the Client ID and Client Secret to your `.env` file
4. Set the required scopes (accounting.transactions, accounting.contacts.read, etc.)

### 3. Start the Application

```bash
cd flinkk-one-backend
python main.py
```

The Xero integration endpoints will be available at:
- Authentication: `http://localhost:8000/api/xero/auth/*`
- Accounting: `http://localhost:8000/api/xero/accounting/*`

## API Endpoints

### Authentication Endpoints

- `GET /api/xero/auth/authorize` - Initiate OAuth2 flow
- `GET /api/xero/auth/callback` - Handle OAuth2 callback
- `POST /api/xero/auth/refresh` - Refresh access token
- `GET /api/xero/auth/connections` - Get user connections
- `POST /api/xero/auth/logout` - Logout and clear session
- `GET /api/xero/auth/status` - Get authentication status

### Accounting Endpoints

- `GET /api/xero/accounting/organizations` - Get organizations
- `GET /api/xero/accounting/invoices` - Get invoices with filtering
- `GET /api/xero/accounting/invoices/{invoice_id}` - Get specific invoice
- `GET /api/xero/accounting/contacts` - Get contacts with search
- `GET /api/xero/accounting/contacts/{contact_id}` - Get specific contact
- `POST /api/xero/accounting/contacts` - Create new contact
- `PUT /api/xero/accounting/contacts/{contact_id}` - Update contact
- `GET /api/xero/accounting/accounts` - Get chart of accounts
- `GET /api/xero/accounting/tracking-categories` - Get tracking categories
- `GET /api/xero/accounting/health` - Health check

## Usage Examples

### Authentication Flow

```python
# 1. Redirect user to authorization URL
response = requests.get("http://localhost:8000/api/xero/auth/authorize")
# User will be redirected to Xero for authorization

# 2. After authorization, user is redirected back to callback
# Token is automatically stored in session

# 3. Check authentication status
status = requests.get("http://localhost:8000/api/xero/auth/status")
```

### Working with Contacts

```python
import requests

# Get all contacts
contacts = requests.get("http://localhost:8000/api/xero/accounting/contacts")

# Search contacts
search_results = requests.get(
    "http://localhost:8000/api/xero/accounting/contacts",
    params={"search": "<EMAIL>"}
)

# Create a new contact
new_contact = requests.post(
    "http://localhost:8000/api/xero/accounting/contacts",
    json={
        "name": "John Doe",
        "email_address": "<EMAIL>",
        "is_customer": True
    }
)
```

### Working with Invoices

```python
# Get all invoices
invoices = requests.get("http://localhost:8000/api/xero/accounting/invoices")

# Filter invoices by status
paid_invoices = requests.get(
    "http://localhost:8000/api/xero/accounting/invoices",
    params={"statuses": ["PAID"]}
)

# Get specific invoice
invoice = requests.get(
    "http://localhost:8000/api/xero/accounting/invoices/invoice-id-here"
)
```

## Architecture

### Module Structure

```
integrations/xero/
├── __init__.py              # Main module exports
├── README.md               # This file
├── config.py               # Configuration management
├── client.py               # Xero API client
├── exceptions.py           # Custom exceptions
├── security.py             # Security utilities
├── auth/                   # Authentication components
│   ├── __init__.py
│   ├── oauth.py           # OAuth2 flow management
│   └── middleware.py      # FastAPI middleware
├── models/                 # Pydantic models
│   ├── __init__.py
│   ├── auth.py            # Authentication models
│   └── accounting.py      # Accounting API models
├── services/               # Business logic services
│   ├── __init__.py
│   ├── base.py            # Base service class
│   ├── identity.py        # Identity API service
│   ├── accounting.py      # Accounting API service
│   └── contacts.py        # Contacts service
└── routers/                # FastAPI routers
    ├── __init__.py
    ├── auth.py            # Authentication endpoints
    └── accounting.py      # Accounting endpoints
```

### Key Components

1. **XeroClient**: Central client for API access with token management
2. **XeroOAuth2Manager**: Handles OAuth2 flow and token operations
3. **Service Classes**: Business logic for different Xero APIs
4. **Security Utilities**: Token encryption, state validation, request security
5. **FastAPI Routers**: RESTful endpoints with proper error handling

## Security Considerations

- **Token Encryption**: Access tokens are encrypted before storage
- **State Validation**: OAuth2 state parameters are signed and validated
- **Rate Limiting**: Built-in support for API rate limiting
- **Input Validation**: All inputs are validated using Pydantic models
- **Error Handling**: Sensitive information is not exposed in error messages

## Error Handling

The integration provides comprehensive error handling with custom exceptions:

- `XeroAPIError`: General API errors
- `XeroAuthenticationError`: Authentication failures
- `XeroTokenError`: Token-related errors
- `XeroRateLimitError`: Rate limiting errors
- `XeroConfigurationError`: Configuration issues

## Logging

Detailed logging is provided for:
- Authentication flows
- API requests and responses
- Error conditions
- Security events

Configure logging level with the `XERO_LOG_LEVEL` environment variable.

## Testing

The integration includes comprehensive health check endpoints:

```bash
# Test authentication
curl http://localhost:8000/api/xero/auth/status

# Test accounting services
curl http://localhost:8000/api/xero/accounting/health
```

## Production Deployment

For production deployment:

1. Use HTTPS for all endpoints
2. Configure proper CORS settings
3. Set up secure session storage (Redis recommended)
4. Implement proper database storage for tokens
5. Configure monitoring and alerting
6. Set up webhook endpoints for real-time updates

## Support

For issues and questions:
1. Check the logs for detailed error information
2. Verify your Xero app configuration
3. Ensure all environment variables are properly set
4. Review the Xero API documentation for specific requirements
