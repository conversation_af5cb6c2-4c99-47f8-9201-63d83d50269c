"""
Xero Security Utilities

Security utilities for Xero integration including token encryption,
validation, and security middleware.
"""

import logging
import secrets
import hashlib
import hmac
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from cryptography.fernet import Fe<PERSON>t
from fastapi import HTTPException, status
import base64

from .config import xero_config
from .exceptions import XeroTokenError, XeroAuthenticationError

logger = logging.getLogger(__name__)


class XeroTokenSecurity:
    """
    Security utilities for Xero token management.
    """
    
    def __init__(self):
        """Initialize token security."""
        self._fernet = None
        self._init_encryption()
    
    def _init_encryption(self) -> None:
        """Initialize encryption cipher."""
        try:
            if xero_config.token_encryption_key:
                # Ensure key is properly formatted for Fernet
                key = xero_config.token_encryption_key.encode()
                if len(key) != 32:
                    # Hash the key to get exactly 32 bytes
                    key = hashlib.sha256(key).digest()
                
                # Encode as base64 for <PERSON>rnet
                key_b64 = base64.urlsafe_b64encode(key)
                self._fernet = Fernet(key_b64)
            else:
                logger.warning("No token encryption key configured - tokens will not be encrypted")
        except Exception as e:
            logger.error(f"Failed to initialize token encryption: {str(e)}")
            self._fernet = None
    
    def encrypt_token(self, token_data: str) -> str:
        """
        Encrypt token data.
        
        Args:
            token_data: Token data to encrypt
            
        Returns:
            Encrypted token data
        """
        if not self._fernet:
            logger.warning("Token encryption not available - returning plain text")
            return token_data
        
        try:
            encrypted = self._fernet.encrypt(token_data.encode())
            return base64.urlsafe_b64encode(encrypted).decode()
        except Exception as e:
            logger.error(f"Failed to encrypt token: {str(e)}")
            raise XeroTokenError("Failed to encrypt token data")
    
    def decrypt_token(self, encrypted_token: str) -> str:
        """
        Decrypt token data.
        
        Args:
            encrypted_token: Encrypted token data
            
        Returns:
            Decrypted token data
        """
        if not self._fernet:
            logger.warning("Token encryption not available - returning as-is")
            return encrypted_token
        
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_token.encode())
            decrypted = self._fernet.decrypt(encrypted_bytes)
            return decrypted.decode()
        except Exception as e:
            logger.error(f"Failed to decrypt token: {str(e)}")
            raise XeroTokenError("Failed to decrypt token data")
    
    def validate_token_expiry(self, expires_at: Optional[datetime]) -> bool:
        """
        Validate if token is still valid (not expired).
        
        Args:
            expires_at: Token expiration datetime
            
        Returns:
            True if token is valid, False if expired
        """
        if not expires_at:
            # If no expiry time, assume token is valid
            return True
        
        # Add 5 minute buffer for clock skew
        buffer_time = timedelta(minutes=5)
        return datetime.utcnow() + buffer_time < expires_at
    
    def is_token_refresh_needed(self, expires_at: Optional[datetime]) -> bool:
        """
        Check if token needs to be refreshed soon.
        
        Args:
            expires_at: Token expiration datetime
            
        Returns:
            True if token should be refreshed, False otherwise
        """
        if not expires_at:
            return False
        
        # Refresh if token expires within 10 minutes
        refresh_threshold = timedelta(minutes=10)
        return datetime.utcnow() + refresh_threshold >= expires_at


class XeroStateSecurity:
    """
    Security utilities for OAuth2 state parameter management.
    """
    
    @staticmethod
    def generate_state() -> str:
        """
        Generate a secure random state parameter.
        
        Returns:
            Random state string
        """
        return secrets.token_urlsafe(32)
    
    @staticmethod
    def generate_state_with_signature(data: Optional[str] = None) -> str:
        """
        Generate state with HMAC signature for validation.
        
        Args:
            data: Optional data to include in state
            
        Returns:
            Signed state string
        """
        timestamp = str(int(datetime.utcnow().timestamp()))
        state_data = f"{timestamp}:{data or ''}"
        
        # Create HMAC signature
        secret = xero_config.state_secret.encode()
        signature = hmac.new(
            secret,
            state_data.encode(),
            hashlib.sha256
        ).hexdigest()
        
        # Combine data and signature
        signed_state = f"{state_data}:{signature}"
        
        # Encode as base64 for URL safety
        return base64.urlsafe_b64encode(signed_state.encode()).decode()
    
    @staticmethod
    def validate_state_signature(state: str, max_age_minutes: int = 10) -> bool:
        """
        Validate state parameter signature and age.
        
        Args:
            state: State parameter to validate
            max_age_minutes: Maximum age in minutes
            
        Returns:
            True if state is valid, False otherwise
        """
        try:
            # Decode from base64
            decoded_state = base64.urlsafe_b64decode(state.encode()).decode()
            
            # Split into components
            parts = decoded_state.split(':')
            if len(parts) < 3:
                return False
            
            timestamp_str = parts[0]
            data = ':'.join(parts[1:-1])  # Rejoin in case data contained colons
            provided_signature = parts[-1]
            
            # Validate timestamp
            timestamp = int(timestamp_str)
            current_time = int(datetime.utcnow().timestamp())
            max_age_seconds = max_age_minutes * 60
            
            if current_time - timestamp > max_age_seconds:
                logger.warning("State parameter expired")
                return False
            
            # Validate signature
            state_data = f"{timestamp_str}:{data}"
            secret = xero_config.state_secret.encode()
            expected_signature = hmac.new(
                secret,
                state_data.encode(),
                hashlib.sha256
            ).hexdigest()
            
            return hmac.compare_digest(provided_signature, expected_signature)
            
        except Exception as e:
            logger.error(f"Failed to validate state signature: {str(e)}")
            return False


class XeroRequestValidator:
    """
    Request validation utilities for Xero integration.
    """
    
    @staticmethod
    def validate_tenant_access(user_id: str, tenant_id: str) -> bool:
        """
        Validate that user has access to the specified tenant.
        
        Args:
            user_id: User ID
            tenant_id: Xero tenant ID
            
        Returns:
            True if access is allowed, False otherwise
            
        Note:
            This is a placeholder implementation. In production,
            you should implement proper tenant access validation
            based on your user management system.
        """
        # TODO: Implement actual tenant access validation
        # This should check against your user/tenant relationship database
        logger.info(f"Validating tenant access for user {user_id} to tenant {tenant_id}")
        return True
    
    @staticmethod
    def validate_api_request_rate_limit(user_id: str, endpoint: str) -> bool:
        """
        Validate API request rate limits.
        
        Args:
            user_id: User ID making the request
            endpoint: API endpoint being accessed
            
        Returns:
            True if request is allowed, False if rate limited
            
        Note:
            This is a placeholder implementation. In production,
            you should implement proper rate limiting using Redis
            or similar storage.
        """
        # TODO: Implement actual rate limiting
        logger.debug(f"Rate limit check for user {user_id} on endpoint {endpoint}")
        return True
    
    @staticmethod
    def sanitize_webhook_data(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Sanitize webhook data for security.
        
        Args:
            data: Webhook data to sanitize
            
        Returns:
            Sanitized data
        """
        # Remove potentially dangerous fields
        dangerous_fields = ['__proto__', 'constructor', 'prototype']
        
        def clean_dict(d):
            if isinstance(d, dict):
                return {
                    k: clean_dict(v) for k, v in d.items()
                    if k not in dangerous_fields
                }
            elif isinstance(d, list):
                return [clean_dict(item) for item in d]
            else:
                return d
        
        return clean_dict(data)


# Global instances
token_security = XeroTokenSecurity()
state_security = XeroStateSecurity()
request_validator = XeroRequestValidator()


def validate_xero_webhook_signature(
    payload: bytes,
    signature: str,
    webhook_key: str
) -> bool:
    """
    Validate Xero webhook signature.
    
    Args:
        payload: Raw webhook payload
        signature: Provided signature header
        webhook_key: Webhook signing key
        
    Returns:
        True if signature is valid, False otherwise
    """
    try:
        # Xero uses HMAC-SHA256 for webhook signatures
        expected_signature = hmac.new(
            webhook_key.encode(),
            payload,
            hashlib.sha256
        ).hexdigest()
        
        # Remove 'sha256=' prefix if present
        if signature.startswith('sha256='):
            signature = signature[7:]
        
        return hmac.compare_digest(signature, expected_signature)
        
    except Exception as e:
        logger.error(f"Failed to validate webhook signature: {str(e)}")
        return False
