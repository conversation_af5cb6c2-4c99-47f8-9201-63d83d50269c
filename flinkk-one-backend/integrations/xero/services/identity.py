"""
Xero Identity Service

Service for Xero Identity API operations including
user information, connections, and tenant management.
"""

import logging
from typing import List, Dict, Any, Optional

from .base import BaseXeroService
from ..models.auth import XeroConnectionInfo, XeroUserInfo
from ..exceptions import XeroAPIError

logger = logging.getLogger(__name__)


class XeroIdentityService(BaseXeroService):
    """
    Service for Xero Identity API operations.
    """
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check by getting connections.
        
        Returns:
            Health check results
        """
        try:
            self._ensure_authenticated()
            connections = await self.get_connections()
            
            return {
                "service": "XeroIdentityService",
                "status": "healthy",
                "connections_count": len(connections),
                "authenticated": True
            }
        except Exception as e:
            return {
                "service": "XeroIdentityService", 
                "status": "unhealthy",
                "error": str(e),
                "authenticated": False
            }
    
    async def get_connections(self) -> List[XeroConnectionInfo]:
        """
        Get all Xero connections for the authenticated user.
        
        Returns:
            List of connection information
            
        Raises:
            XeroAPIError: If API call fails
        """
        try:
            self._ensure_authenticated()
            
            connections = self.client.identity_api.get_connections()
            
            result = []
            for conn in connections:
                connection_info = XeroConnectionInfo(
                    id=conn.id,
                    tenant_id=conn.tenant_id,
                    tenant_type=conn.tenant_type,
                    tenant_name=getattr(conn, 'tenant_name', None),
                    created_date_utc=getattr(conn, 'created_date_utc', None),
                    updated_date_utc=getattr(conn, 'updated_date_utc', None)
                )
                result.append(connection_info)
            
            self.logger.info(f"Retrieved {len(result)} Xero connections")
            return result
            
        except Exception as e:
            self._handle_api_exception(e, "get connections")
    
    async def get_user_info(self) -> XeroUserInfo:
        """
        Get user information from Xero.
        
        Returns:
            User information
            
        Raises:
            XeroAPIError: If API call fails
        """
        try:
            self._ensure_authenticated()
            
            # Get user info from Identity API
            user_info = self.client.identity_api.get_current_user()
            
            return XeroUserInfo(
                user_id=user_info.user_id,
                username=getattr(user_info, 'username', None),
                given_name=getattr(user_info, 'given_name', None),
                family_name=getattr(user_info, 'family_name', None),
                email=getattr(user_info, 'email', None),
                active_session=getattr(user_info, 'active_session', None)
            )
            
        except Exception as e:
            self._handle_api_exception(e, "get user info")
    
    async def get_organization_connections(self) -> List[XeroConnectionInfo]:
        """
        Get only organization connections (filter out other types).
        
        Returns:
            List of organization connections
        """
        connections = await self.get_connections()
        return [conn for conn in connections if conn.tenant_type == "ORGANISATION"]
    
    async def get_primary_tenant_id(self) -> Optional[str]:
        """
        Get the primary tenant ID (first organization connection).
        
        Returns:
            Primary tenant ID if available, None otherwise
        """
        try:
            org_connections = await self.get_organization_connections()
            if org_connections:
                return org_connections[0].tenant_id
            return None
        except Exception as e:
            self.logger.error(f"Failed to get primary tenant ID: {str(e)}")
            return None
    
    async def validate_connection(self, tenant_id: str) -> bool:
        """
        Validate that a tenant ID is accessible by the current user.
        
        Args:
            tenant_id: Tenant ID to validate
            
        Returns:
            True if connection is valid, False otherwise
        """
        try:
            connections = await self.get_connections()
            return any(conn.tenant_id == tenant_id for conn in connections)
        except Exception as e:
            self.logger.error(f"Failed to validate connection: {str(e)}")
            return False
    
    async def disconnect_tenant(self, connection_id: str) -> bool:
        """
        Disconnect a specific tenant connection.
        
        Args:
            connection_id: Connection ID to disconnect
            
        Returns:
            True if successful, False otherwise
            
        Note:
            This operation requires the connection to be disconnected
            from the Xero side as well.
        """
        try:
            self._ensure_authenticated()
            
            # Note: The Xero Python SDK doesn't have a direct disconnect method
            # This would typically be handled through the Xero web interface
            # or by revoking the token
            
            self.logger.warning(
                f"Disconnect operation for connection {connection_id} "
                "should be handled through Xero web interface"
            )
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to disconnect tenant: {str(e)}")
            return False
