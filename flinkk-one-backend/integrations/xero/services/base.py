"""
Base Xero Service

Provides base functionality for all Xero service classes
including error handling, logging, and common operations.
"""

import logging
from typing import Optional, Any, Dict, List
from abc import ABC, abstractmethod

from ..client import XeroClient
from ..exceptions import XeroAPIError, XeroTokenError
from xero_python.api_client.oauth2 import OAuth2Token
from xero_python.exceptions import ApiException

logger = logging.getLogger(__name__)


class BaseXeroService(ABC):
    """
    Base class for all Xero services providing common functionality.
    """
    
    def __init__(self, xero_client: XeroClient):
        """
        Initialize base service.
        
        Args:
            xero_client: Configured Xero client instance
        """
        self.client = xero_client
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def _ensure_authenticated(self) -> None:
        """
        Ensure the client is authenticated.
        
        Raises:
            XeroTokenError: If client is not authenticated
        """
        if not self.client.is_authenticated():
            raise XeroTokenError("Xero client is not authenticated")
    
    def _get_tenant_id(self) -> str:
        """
        Get the tenant ID for API calls.
        
        Returns:
            Tenant ID
            
        Raises:
            XeroAPIError: If tenant ID cannot be retrieved
        """
        tenant_id = self.client.get_tenant_id()
        if not tenant_id:
            raise XeroAPIError("Unable to retrieve Xero tenant ID")
        return tenant_id
    
    def _handle_api_exception(self, e: Exception, operation: str) -> None:
        """
        Handle API exceptions with proper logging and error conversion.
        
        Args:
            e: Exception that occurred
            operation: Description of the operation that failed
            
        Raises:
            XeroAPIError: Converted API error
        """
        self.logger.error(f"Xero API error during {operation}: {str(e)}")
        
        if isinstance(e, ApiException):
            # Extract error details from Xero API exception
            error_details = {
                "status_code": e.status,
                "reason": e.reason,
                "body": e.body
            }
            
            # Handle specific error codes
            if e.status == 401:
                raise XeroTokenError(f"Authentication failed during {operation}")
            elif e.status == 403:
                raise XeroAPIError(f"Access forbidden during {operation}", 
                                 status_code=e.status, response_data=error_details)
            elif e.status == 429:
                from ..exceptions import XeroRateLimitError
                raise XeroRateLimitError(f"Rate limit exceeded during {operation}",
                                       status_code=e.status, response_data=error_details)
            else:
                raise XeroAPIError(f"API error during {operation}: {e.reason}",
                                 status_code=e.status, response_data=error_details)
        else:
            raise XeroAPIError(f"Unexpected error during {operation}: {str(e)}")
    
    async def _retry_with_token_refresh(self, operation_func, *args, **kwargs):
        """
        Retry an operation with token refresh if authentication fails.
        
        Args:
            operation_func: Function to retry
            *args: Arguments for the function
            **kwargs: Keyword arguments for the function
            
        Returns:
            Result of the operation
        """
        try:
            return await operation_func(*args, **kwargs)
        except XeroTokenError:
            # Try to refresh token and retry once
            self.logger.info("Attempting to refresh Xero token and retry operation")
            try:
                self.client.refresh_token()
                return await operation_func(*args, **kwargs)
            except Exception as e:
                self.logger.error(f"Token refresh and retry failed: {str(e)}")
                raise
    
    def _serialize_response(self, response: Any) -> Dict[str, Any]:
        """
        Serialize Xero API response to dictionary.
        
        Args:
            response: Xero API response object
            
        Returns:
            Serialized response data
        """
        try:
            from xero_python.api_client import serialize
            return serialize(response)
        except Exception as e:
            self.logger.warning(f"Failed to serialize response: {str(e)}")
            return {"raw_response": str(response)}
    
    def _validate_required_fields(self, data: Dict[str, Any], required_fields: List[str]) -> None:
        """
        Validate that required fields are present in data.
        
        Args:
            data: Data to validate
            required_fields: List of required field names
            
        Raises:
            ValueError: If required fields are missing
        """
        missing_fields = [field for field in required_fields if field not in data or data[field] is None]
        if missing_fields:
            raise ValueError(f"Missing required fields: {', '.join(missing_fields)}")
    
    @abstractmethod
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check for the service.
        
        Returns:
            Health check results
        """
        pass
