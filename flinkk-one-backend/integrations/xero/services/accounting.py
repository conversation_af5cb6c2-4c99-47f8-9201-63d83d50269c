"""
Xero Accounting Service

Service for Xero Accounting API operations including
organizations, invoices, and general accounting data.
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, date

from .base import BaseXeroService
from ..models.accounting import XeroOrganization, XeroInvoice, InvoiceStatus
from ..exceptions import XeroAPIError
from xero_python.accounting import AccountingApi

logger = logging.getLogger(__name__)


class XeroAccountingService(BaseXeroService):
    """
    Service for Xero Accounting API operations.
    """
    
    @property
    def accounting_api(self) -> AccountingApi:
        """Get the accounting API instance."""
        return self.client.accounting_api
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check by getting organizations.
        
        Returns:
            Health check results
        """
        try:
            self._ensure_authenticated()
            tenant_id = self._get_tenant_id()
            organizations = await self.get_organizations(tenant_id)
            
            return {
                "service": "XeroAccountingService",
                "status": "healthy",
                "tenant_id": tenant_id,
                "organizations_count": len(organizations),
                "authenticated": True
            }
        except Exception as e:
            return {
                "service": "XeroAccountingService",
                "status": "unhealthy", 
                "error": str(e),
                "authenticated": False
            }
    
    async def get_organizations(self, tenant_id: Optional[str] = None) -> List[XeroOrganization]:
        """
        Get organizations for the specified tenant.
        
        Args:
            tenant_id: Xero tenant ID (uses default if not provided)
            
        Returns:
            List of organizations
            
        Raises:
            XeroAPIError: If API call fails
        """
        try:
            self._ensure_authenticated()
            
            if not tenant_id:
                tenant_id = self._get_tenant_id()
            
            organizations = self.accounting_api.get_organisations(xero_tenant_id=tenant_id)
            
            result = []
            for org in organizations.organisations:
                organization = XeroOrganization(
                    organisation_id=getattr(org, 'organisation_id', None),
                    api_key=getattr(org, 'api_key', None),
                    name=getattr(org, 'name', None),
                    legal_name=getattr(org, 'legal_name', None),
                    pays_tax=getattr(org, 'pays_tax', None),
                    version=getattr(org, 'version', None),
                    organisation_type=getattr(org, 'organisation_type', None),
                    base_currency=getattr(org, 'base_currency', None),
                    country_code=getattr(org, 'country_code', None),
                    is_demo_company=getattr(org, 'is_demo_company', None),
                    organisation_status=getattr(org, 'organisation_status', None),
                    registration_number=getattr(org, 'registration_number', None),
                    employer_identification_number=getattr(org, 'employer_identification_number', None),
                    tax_number=getattr(org, 'tax_number', None),
                    financial_year_end_day=getattr(org, 'financial_year_end_day', None),
                    financial_year_end_month=getattr(org, 'financial_year_end_month', None),
                    sales_tax_basis=getattr(org, 'sales_tax_basis', None),
                    sales_tax_period=getattr(org, 'sales_tax_period', None),
                    default_sales_tax=getattr(org, 'default_sales_tax', None),
                    default_purchases_tax=getattr(org, 'default_purchases_tax', None),
                    period_lock_date=getattr(org, 'period_lock_date', None),
                    end_of_year_lock_date=getattr(org, 'end_of_year_lock_date', None),
                    created_date_utc=getattr(org, 'created_date_utc', None),
                    timezone=getattr(org, 'timezone', None),
                    organisation_entity_type=getattr(org, 'organisation_entity_type', None),
                    short_code=getattr(org, 'short_code', None),
                    line_of_business=getattr(org, 'line_of_business', None),
                    addresses=getattr(org, 'addresses', None),
                    phones=getattr(org, 'phones', None),
                    external_links=getattr(org, 'external_links', None)
                )
                result.append(organization)
            
            self.logger.info(f"Retrieved {len(result)} organizations for tenant {tenant_id}")
            return result
            
        except Exception as e:
            self._handle_api_exception(e, f"get organizations for tenant {tenant_id}")
    
    async def get_invoices(
        self,
        tenant_id: Optional[str] = None,
        statuses: Optional[List[InvoiceStatus]] = None,
        page: int = 1,
        limit: Optional[int] = None,
        modified_since: Optional[datetime] = None
    ) -> List[XeroInvoice]:
        """
        Get invoices with optional filtering.
        
        Args:
            tenant_id: Xero tenant ID (uses default if not provided)
            statuses: List of invoice statuses to filter by
            page: Page number for pagination
            limit: Maximum number of results
            modified_since: Only return invoices modified since this date
            
        Returns:
            List of invoices
            
        Raises:
            XeroAPIError: If API call fails
        """
        try:
            self._ensure_authenticated()
            
            if not tenant_id:
                tenant_id = self._get_tenant_id()
            
            # Prepare parameters
            kwargs = {
                'xero_tenant_id': tenant_id,
                'page': page
            }
            
            if statuses:
                kwargs['statuses'] = [status.value for status in statuses]
            
            if modified_since:
                kwargs['if_modified_since'] = modified_since
            
            if limit:
                kwargs['where'] = f"Type=\"ACCREC\""  # Only sales invoices by default
            
            invoices = self.accounting_api.get_invoices(**kwargs)
            
            result = []
            for inv in invoices.invoices:
                invoice = XeroInvoice(
                    invoice_id=getattr(inv, 'invoice_id', None),
                    invoice_number=getattr(inv, 'invoice_number', None),
                    reference=getattr(inv, 'reference', None),
                    type=getattr(inv, 'type', None),
                    contact=getattr(inv, 'contact', None),
                    date=getattr(inv, 'date', None),
                    due_date=getattr(inv, 'due_date', None),
                    line_amount_types=getattr(inv, 'line_amount_types', None),
                    line_items=getattr(inv, 'line_items', None),
                    sub_total=getattr(inv, 'sub_total', None),
                    total_tax=getattr(inv, 'total_tax', None),
                    total=getattr(inv, 'total', None),
                    total_discount=getattr(inv, 'total_discount', None),
                    currency_code=getattr(inv, 'currency_code', None),
                    currency_rate=getattr(inv, 'currency_rate', None),
                    status=getattr(inv, 'status', None),
                    sent_to_contact=getattr(inv, 'sent_to_contact', None),
                    expected_payment_date=getattr(inv, 'expected_payment_date', None),
                    planned_payment_date=getattr(inv, 'planned_payment_date', None),
                    updated_date_utc=getattr(inv, 'updated_date_utc', None)
                )
                result.append(invoice)
            
            self.logger.info(f"Retrieved {len(result)} invoices for tenant {tenant_id}")
            return result
            
        except Exception as e:
            self._handle_api_exception(e, f"get invoices for tenant {tenant_id}")
    
    async def get_invoice_by_id(
        self,
        invoice_id: str,
        tenant_id: Optional[str] = None
    ) -> Optional[XeroInvoice]:
        """
        Get a specific invoice by ID.
        
        Args:
            invoice_id: Invoice ID to retrieve
            tenant_id: Xero tenant ID (uses default if not provided)
            
        Returns:
            Invoice if found, None otherwise
            
        Raises:
            XeroAPIError: If API call fails
        """
        try:
            self._ensure_authenticated()
            
            if not tenant_id:
                tenant_id = self._get_tenant_id()
            
            invoice = self.accounting_api.get_invoice(
                xero_tenant_id=tenant_id,
                invoice_id=invoice_id
            )
            
            if invoice.invoices:
                inv = invoice.invoices[0]
                return XeroInvoice(
                    invoice_id=getattr(inv, 'invoice_id', None),
                    invoice_number=getattr(inv, 'invoice_number', None),
                    reference=getattr(inv, 'reference', None),
                    type=getattr(inv, 'type', None),
                    contact=getattr(inv, 'contact', None),
                    date=getattr(inv, 'date', None),
                    due_date=getattr(inv, 'due_date', None),
                    line_amount_types=getattr(inv, 'line_amount_types', None),
                    line_items=getattr(inv, 'line_items', None),
                    sub_total=getattr(inv, 'sub_total', None),
                    total_tax=getattr(inv, 'total_tax', None),
                    total=getattr(inv, 'total', None),
                    total_discount=getattr(inv, 'total_discount', None),
                    currency_code=getattr(inv, 'currency_code', None),
                    currency_rate=getattr(inv, 'currency_rate', None),
                    status=getattr(inv, 'status', None),
                    sent_to_contact=getattr(inv, 'sent_to_contact', None),
                    expected_payment_date=getattr(inv, 'expected_payment_date', None),
                    planned_payment_date=getattr(inv, 'planned_payment_date', None),
                    updated_date_utc=getattr(inv, 'updated_date_utc', None)
                )
            
            return None
            
        except Exception as e:
            self._handle_api_exception(e, f"get invoice {invoice_id} for tenant {tenant_id}")
    
    async def get_accounts(self, tenant_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get chart of accounts.
        
        Args:
            tenant_id: Xero tenant ID (uses default if not provided)
            
        Returns:
            List of accounts
            
        Raises:
            XeroAPIError: If API call fails
        """
        try:
            self._ensure_authenticated()
            
            if not tenant_id:
                tenant_id = self._get_tenant_id()
            
            accounts = self.accounting_api.get_accounts(xero_tenant_id=tenant_id)
            
            result = []
            for account in accounts.accounts:
                result.append(self._serialize_response(account))
            
            self.logger.info(f"Retrieved {len(result)} accounts for tenant {tenant_id}")
            return result
            
        except Exception as e:
            self._handle_api_exception(e, f"get accounts for tenant {tenant_id}")
    
    async def get_tracking_categories(self, tenant_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get tracking categories.
        
        Args:
            tenant_id: Xero tenant ID (uses default if not provided)
            
        Returns:
            List of tracking categories
            
        Raises:
            XeroAPIError: If API call fails
        """
        try:
            self._ensure_authenticated()
            
            if not tenant_id:
                tenant_id = self._get_tenant_id()
            
            tracking_categories = self.accounting_api.get_tracking_categories(xero_tenant_id=tenant_id)
            
            result = []
            for category in tracking_categories.tracking_categories:
                result.append(self._serialize_response(category))
            
            self.logger.info(f"Retrieved {len(result)} tracking categories for tenant {tenant_id}")
            return result
            
        except Exception as e:
            self._handle_api_exception(e, f"get tracking categories for tenant {tenant_id}")
