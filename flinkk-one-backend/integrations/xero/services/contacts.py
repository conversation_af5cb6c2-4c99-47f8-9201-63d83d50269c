"""
Xero Contacts Service

Service for Xero Contacts API operations including
contact management, creation, updates, and queries.
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from .base import BaseXeroService
from ..models.accounting import XeroContact, ContactStatus
from ..exceptions import XeroAPIError
from xero_python.accounting import Contact, Contacts, ContactPerson

logger = logging.getLogger(__name__)


class XeroContactsService(BaseXeroService):
    """
    Service for Xero Contacts API operations.
    """
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check by getting contacts count.
        
        Returns:
            Health check results
        """
        try:
            self._ensure_authenticated()
            tenant_id = self._get_tenant_id()
            contacts = await self.get_contacts(tenant_id, limit=1)
            
            return {
                "service": "XeroContactsService",
                "status": "healthy",
                "tenant_id": tenant_id,
                "can_access_contacts": True,
                "authenticated": True
            }
        except Exception as e:
            return {
                "service": "XeroContactsService",
                "status": "unhealthy",
                "error": str(e),
                "authenticated": False
            }
    
    async def get_contacts(
        self,
        tenant_id: Optional[str] = None,
        page: int = 1,
        limit: Optional[int] = None,
        modified_since: Optional[datetime] = None,
        where: Optional[str] = None,
        order: Optional[str] = None,
        include_archived: bool = False
    ) -> List[XeroContact]:
        """
        Get contacts with optional filtering and pagination.
        
        Args:
            tenant_id: Xero tenant ID (uses default if not provided)
            page: Page number for pagination
            limit: Maximum number of results
            modified_since: Only return contacts modified since this date
            where: WHERE clause for filtering
            order: ORDER BY clause for sorting
            include_archived: Include archived contacts
            
        Returns:
            List of contacts
            
        Raises:
            XeroAPIError: If API call fails
        """
        try:
            self._ensure_authenticated()
            
            if not tenant_id:
                tenant_id = self._get_tenant_id()
            
            # Prepare parameters
            kwargs = {
                'xero_tenant_id': tenant_id,
                'page': page
            }
            
            if modified_since:
                kwargs['if_modified_since'] = modified_since
            
            if where:
                kwargs['where'] = where
            elif not include_archived:
                kwargs['where'] = 'ContactStatus="ACTIVE"'
            
            if order:
                kwargs['order'] = order
            
            contacts = self.client.accounting_api.get_contacts(**kwargs)
            
            result = []
            for contact in contacts.contacts:
                xero_contact = self._convert_to_xero_contact(contact)
                result.append(xero_contact)
            
            self.logger.info(f"Retrieved {len(result)} contacts for tenant {tenant_id}")
            return result
            
        except Exception as e:
            self._handle_api_exception(e, f"get contacts for tenant {tenant_id}")
    
    async def get_contact_by_id(
        self,
        contact_id: str,
        tenant_id: Optional[str] = None
    ) -> Optional[XeroContact]:
        """
        Get a specific contact by ID.
        
        Args:
            contact_id: Contact ID to retrieve
            tenant_id: Xero tenant ID (uses default if not provided)
            
        Returns:
            Contact if found, None otherwise
            
        Raises:
            XeroAPIError: If API call fails
        """
        try:
            self._ensure_authenticated()
            
            if not tenant_id:
                tenant_id = self._get_tenant_id()
            
            contact = self.client.accounting_api.get_contact(
                xero_tenant_id=tenant_id,
                contact_id=contact_id
            )
            
            if contact.contacts:
                return self._convert_to_xero_contact(contact.contacts[0])
            
            return None
            
        except Exception as e:
            self._handle_api_exception(e, f"get contact {contact_id} for tenant {tenant_id}")
    
    async def search_contacts(
        self,
        search_term: str,
        tenant_id: Optional[str] = None,
        limit: int = 50
    ) -> List[XeroContact]:
        """
        Search contacts by name or email.
        
        Args:
            search_term: Term to search for
            tenant_id: Xero tenant ID (uses default if not provided)
            limit: Maximum number of results
            
        Returns:
            List of matching contacts
            
        Raises:
            XeroAPIError: If API call fails
        """
        try:
            # Build WHERE clause for search
            where_clause = (
                f'Name.Contains("{search_term}") OR '
                f'EmailAddress.Contains("{search_term}") OR '
                f'FirstName.Contains("{search_term}") OR '
                f'LastName.Contains("{search_term}")'
            )
            
            return await self.get_contacts(
                tenant_id=tenant_id,
                where=where_clause,
                limit=limit
            )
            
        except Exception as e:
            self._handle_api_exception(e, f"search contacts with term '{search_term}'")
    
    async def create_contact(
        self,
        contact_data: Dict[str, Any],
        tenant_id: Optional[str] = None
    ) -> XeroContact:
        """
        Create a new contact.
        
        Args:
            contact_data: Contact data dictionary
            tenant_id: Xero tenant ID (uses default if not provided)
            
        Returns:
            Created contact
            
        Raises:
            XeroAPIError: If API call fails
        """
        try:
            self._ensure_authenticated()
            
            if not tenant_id:
                tenant_id = self._get_tenant_id()
            
            # Validate required fields
            self._validate_required_fields(contact_data, ['name'])
            
            # Create Contact object
            contact = Contact(
                name=contact_data['name'],
                first_name=contact_data.get('first_name'),
                last_name=contact_data.get('last_name'),
                email_address=contact_data.get('email_address'),
                contact_number=contact_data.get('contact_number'),
                account_number=contact_data.get('account_number'),
                is_supplier=contact_data.get('is_supplier', False),
                is_customer=contact_data.get('is_customer', True),
                default_currency=contact_data.get('default_currency'),
                tax_number=contact_data.get('tax_number'),
                bank_account_details=contact_data.get('bank_account_details')
            )
            
            # Add contact persons if provided
            if 'contact_persons' in contact_data:
                contact_persons = []
                for cp_data in contact_data['contact_persons']:
                    contact_person = ContactPerson(
                        first_name=cp_data.get('first_name'),
                        last_name=cp_data.get('last_name'),
                        email_address=cp_data.get('email_address'),
                        include_in_emails=cp_data.get('include_in_emails', True)
                    )
                    contact_persons.append(contact_person)
                contact.contact_persons = contact_persons
            
            contacts = Contacts(contacts=[contact])
            
            created_contacts = self.client.accounting_api.create_contacts(
                xero_tenant_id=tenant_id,
                contacts=contacts
            )
            
            if created_contacts.contacts:
                result = self._convert_to_xero_contact(created_contacts.contacts[0])
                self.logger.info(f"Created contact {result.contact_id} for tenant {tenant_id}")
                return result
            
            raise XeroAPIError("Failed to create contact - no contact returned")
            
        except Exception as e:
            self._handle_api_exception(e, f"create contact for tenant {tenant_id}")
    
    async def update_contact(
        self,
        contact_id: str,
        contact_data: Dict[str, Any],
        tenant_id: Optional[str] = None
    ) -> XeroContact:
        """
        Update an existing contact.
        
        Args:
            contact_id: Contact ID to update
            contact_data: Updated contact data
            tenant_id: Xero tenant ID (uses default if not provided)
            
        Returns:
            Updated contact
            
        Raises:
            XeroAPIError: If API call fails
        """
        try:
            self._ensure_authenticated()
            
            if not tenant_id:
                tenant_id = self._get_tenant_id()
            
            # Get existing contact first
            existing_contact = await self.get_contact_by_id(contact_id, tenant_id)
            if not existing_contact:
                raise XeroAPIError(f"Contact {contact_id} not found")
            
            # Create updated Contact object
            contact = Contact(
                contact_id=contact_id,
                name=contact_data.get('name', existing_contact.name),
                first_name=contact_data.get('first_name', existing_contact.first_name),
                last_name=contact_data.get('last_name', existing_contact.last_name),
                email_address=contact_data.get('email_address', existing_contact.email_address),
                contact_number=contact_data.get('contact_number', existing_contact.contact_number),
                account_number=contact_data.get('account_number', existing_contact.account_number),
                is_supplier=contact_data.get('is_supplier', existing_contact.is_supplier),
                is_customer=contact_data.get('is_customer', existing_contact.is_customer),
                default_currency=contact_data.get('default_currency', existing_contact.default_currency),
                tax_number=contact_data.get('tax_number', existing_contact.tax_number),
                bank_account_details=contact_data.get('bank_account_details', existing_contact.bank_account_details)
            )
            
            contacts = Contacts(contacts=[contact])
            
            updated_contacts = self.client.accounting_api.update_contact(
                xero_tenant_id=tenant_id,
                contact_id=contact_id,
                contacts=contacts
            )
            
            if updated_contacts.contacts:
                result = self._convert_to_xero_contact(updated_contacts.contacts[0])
                self.logger.info(f"Updated contact {contact_id} for tenant {tenant_id}")
                return result
            
            raise XeroAPIError("Failed to update contact - no contact returned")
            
        except Exception as e:
            self._handle_api_exception(e, f"update contact {contact_id} for tenant {tenant_id}")
    
    def _convert_to_xero_contact(self, contact: Contact) -> XeroContact:
        """
        Convert Xero Contact object to XeroContact model.
        
        Args:
            contact: Xero Contact object
            
        Returns:
            XeroContact model
        """
        return XeroContact(
            contact_id=getattr(contact, 'contact_id', None),
            contact_number=getattr(contact, 'contact_number', None),
            account_number=getattr(contact, 'account_number', None),
            contact_status=getattr(contact, 'contact_status', None),
            name=getattr(contact, 'name', ''),
            first_name=getattr(contact, 'first_name', None),
            last_name=getattr(contact, 'last_name', None),
            email_address=getattr(contact, 'email_address', None),
            skype_user_name=getattr(contact, 'skype_user_name', None),
            contact_persons=getattr(contact, 'contact_persons', None),
            bank_account_details=getattr(contact, 'bank_account_details', None),
            tax_number=getattr(contact, 'tax_number', None),
            accounts_receivable_tax_type=getattr(contact, 'accounts_receivable_tax_type', None),
            accounts_payable_tax_type=getattr(contact, 'accounts_payable_tax_type', None),
            addresses=getattr(contact, 'addresses', None),
            phones=getattr(contact, 'phones', None),
            is_supplier=getattr(contact, 'is_supplier', None),
            is_customer=getattr(contact, 'is_customer', None),
            default_currency=getattr(contact, 'default_currency', None),
            updated_date_utc=getattr(contact, 'updated_date_utc', None)
        )
