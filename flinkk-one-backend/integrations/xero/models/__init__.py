"""
Xero Integration Models

Pydantic models for Xero integration data structures,
API requests, and responses.
"""

from .auth import (
    XeroAuthState,
    XeroTokenData,
    XeroAuthCallback,
    XeroTokenResponse
)
from .accounting import (
    XeroContact,
    XeroInvoice,
    XeroOrganization,
    XeroTenant
)

__all__ = [
    "XeroAuthState",
    "XeroTokenData", 
    "XeroAuthCallback",
    "XeroTokenResponse",
    "XeroContact",
    "XeroInvoice",
    "XeroOrganization",
    "XeroTenant"
]
