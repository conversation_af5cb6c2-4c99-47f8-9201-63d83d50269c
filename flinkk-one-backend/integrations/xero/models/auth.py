"""
Xero Authentication Models

Pydantic models for Xero OAuth2 authentication flow,
token management, and related data structures.
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, validator


class XeroAuthState(BaseModel):
    """Model for OAuth2 state parameter."""
    
    state: str = Field(..., description="OAuth2 state parameter")
    user_id: Optional[str] = Field(None, description="Associated user ID")
    tenant_id: Optional[str] = Field(None, description="Associated tenant ID")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    expires_at: datetime = Field(..., description="State expiration time")


class XeroTokenData(BaseModel):
    """Model for Xero OAuth2 token data."""
    
    access_token: str = Field(..., description="Access token")
    refresh_token: Optional[str] = Field(None, description="Refresh token")
    token_type: str = Field(default="Bearer", description="Token type")
    expires_in: Optional[int] = Field(None, description="Token expiration in seconds")
    expires_at: Optional[datetime] = Field(None, description="Token expiration timestamp")
    scope: Optional[str] = Field(None, description="Token scope")
    
    @validator("expires_at", pre=True, always=True)
    def set_expires_at(cls, v, values):
        """Set expires_at based on expires_in if not provided."""
        if v is None and "expires_in" in values and values["expires_in"]:
            return datetime.utcnow().timestamp() + values["expires_in"]
        return v


class XeroAuthCallback(BaseModel):
    """Model for OAuth2 callback parameters."""
    
    code: str = Field(..., description="Authorization code")
    state: str = Field(..., description="OAuth2 state parameter")
    scope: Optional[str] = Field(None, description="Granted scope")
    error: Optional[str] = Field(None, description="Error code if any")
    error_description: Optional[str] = Field(None, description="Error description")


class XeroTokenResponse(BaseModel):
    """Model for token response."""
    
    success: bool = Field(..., description="Whether operation was successful")
    token_data: Optional[XeroTokenData] = Field(None, description="Token data if successful")
    error: Optional[str] = Field(None, description="Error message if failed")
    tenant_id: Optional[str] = Field(None, description="Associated Xero tenant ID")


class XeroConnectionInfo(BaseModel):
    """Model for Xero connection information."""
    
    id: str = Field(..., description="Connection ID")
    tenant_id: str = Field(..., description="Tenant ID")
    tenant_type: str = Field(..., description="Tenant type")
    tenant_name: Optional[str] = Field(None, description="Tenant name")
    created_date_utc: Optional[datetime] = Field(None, description="Connection creation date")
    updated_date_utc: Optional[datetime] = Field(None, description="Connection update date")


class XeroUserInfo(BaseModel):
    """Model for Xero user information."""
    
    user_id: str = Field(..., description="User ID")
    username: Optional[str] = Field(None, description="Username")
    given_name: Optional[str] = Field(None, description="Given name")
    family_name: Optional[str] = Field(None, description="Family name")
    email: Optional[str] = Field(None, description="Email address")
    active_session: Optional[bool] = Field(None, description="Whether session is active")
