"""
Xero Accounting Models

Pydantic models for Xero Accounting API data structures
including contacts, invoices, organizations, and other entities.
"""

from datetime import datetime, date
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum


class ContactStatus(str, Enum):
    """Contact status enumeration."""
    ACTIVE = "ACTIVE"
    ARCHIVED = "ARCHIVED"


class InvoiceStatus(str, Enum):
    """Invoice status enumeration."""
    DRAFT = "DRAFT"
    SUBMITTED = "SUBMITTED"
    AUTHORISED = "AUTHORISED"
    PAID = "PAID"
    VOIDED = "VOIDED"


class InvoiceType(str, Enum):
    """Invoice type enumeration."""
    ACCPAY = "ACCPAY"
    ACCPAYCREDIT = "ACCPAYCREDIT"
    ACCREC = "ACCREC"
    ACCRECCREDIT = "ACCRECCREDIT"


class XeroAddress(BaseModel):
    """Model for Xero address."""
    
    address_type: Optional[str] = Field(None, description="Address type")
    address_line1: Optional[str] = Field(None, description="Address line 1")
    address_line2: Optional[str] = Field(None, description="Address line 2")
    address_line3: Optional[str] = Field(None, description="Address line 3")
    address_line4: Optional[str] = Field(None, description="Address line 4")
    city: Optional[str] = Field(None, description="City")
    region: Optional[str] = Field(None, description="Region/State")
    postal_code: Optional[str] = Field(None, description="Postal code")
    country: Optional[str] = Field(None, description="Country")
    attention_to: Optional[str] = Field(None, description="Attention to")


class XeroPhone(BaseModel):
    """Model for Xero phone number."""
    
    phone_type: Optional[str] = Field(None, description="Phone type")
    phone_number: Optional[str] = Field(None, description="Phone number")
    phone_area_code: Optional[str] = Field(None, description="Area code")
    phone_country_code: Optional[str] = Field(None, description="Country code")


class XeroContactPerson(BaseModel):
    """Model for Xero contact person."""
    
    first_name: Optional[str] = Field(None, description="First name")
    last_name: Optional[str] = Field(None, description="Last name")
    email_address: Optional[str] = Field(None, description="Email address")
    include_in_emails: Optional[bool] = Field(None, description="Include in emails")


class XeroContact(BaseModel):
    """Model for Xero contact."""
    
    contact_id: Optional[str] = Field(None, description="Contact ID")
    contact_number: Optional[str] = Field(None, description="Contact number")
    account_number: Optional[str] = Field(None, description="Account number")
    contact_status: Optional[ContactStatus] = Field(None, description="Contact status")
    name: str = Field(..., description="Contact name")
    first_name: Optional[str] = Field(None, description="First name")
    last_name: Optional[str] = Field(None, description="Last name")
    email_address: Optional[str] = Field(None, description="Email address")
    skype_user_name: Optional[str] = Field(None, description="Skype username")
    contact_persons: Optional[List[XeroContactPerson]] = Field(None, description="Contact persons")
    bank_account_details: Optional[str] = Field(None, description="Bank account details")
    tax_number: Optional[str] = Field(None, description="Tax number")
    accounts_receivable_tax_type: Optional[str] = Field(None, description="AR tax type")
    accounts_payable_tax_type: Optional[str] = Field(None, description="AP tax type")
    addresses: Optional[List[XeroAddress]] = Field(None, description="Addresses")
    phones: Optional[List[XeroPhone]] = Field(None, description="Phone numbers")
    is_supplier: Optional[bool] = Field(None, description="Is supplier")
    is_customer: Optional[bool] = Field(None, description="Is customer")
    default_currency: Optional[str] = Field(None, description="Default currency")
    updated_date_utc: Optional[datetime] = Field(None, description="Last updated")
    
    class Config:
        use_enum_values = True


class XeroLineItem(BaseModel):
    """Model for Xero line item."""
    
    line_item_id: Optional[str] = Field(None, description="Line item ID")
    description: Optional[str] = Field(None, description="Description")
    quantity: Optional[float] = Field(None, description="Quantity")
    unit_amount: Optional[float] = Field(None, description="Unit amount")
    item_code: Optional[str] = Field(None, description="Item code")
    account_code: Optional[str] = Field(None, description="Account code")
    tax_type: Optional[str] = Field(None, description="Tax type")
    tax_amount: Optional[float] = Field(None, description="Tax amount")
    line_amount: Optional[float] = Field(None, description="Line amount")
    tracking: Optional[List[Dict[str, Any]]] = Field(None, description="Tracking categories")


class XeroInvoice(BaseModel):
    """Model for Xero invoice."""
    
    invoice_id: Optional[str] = Field(None, description="Invoice ID")
    invoice_number: Optional[str] = Field(None, description="Invoice number")
    reference: Optional[str] = Field(None, description="Reference")
    type: Optional[InvoiceType] = Field(None, description="Invoice type")
    contact: Optional[XeroContact] = Field(None, description="Contact")
    date: Optional[date] = Field(None, description="Invoice date")
    due_date: Optional[date] = Field(None, description="Due date")
    line_amount_types: Optional[str] = Field(None, description="Line amount types")
    line_items: Optional[List[XeroLineItem]] = Field(None, description="Line items")
    sub_total: Optional[float] = Field(None, description="Sub total")
    total_tax: Optional[float] = Field(None, description="Total tax")
    total: Optional[float] = Field(None, description="Total amount")
    total_discount: Optional[float] = Field(None, description="Total discount")
    currency_code: Optional[str] = Field(None, description="Currency code")
    currency_rate: Optional[float] = Field(None, description="Currency rate")
    status: Optional[InvoiceStatus] = Field(None, description="Invoice status")
    sent_to_contact: Optional[bool] = Field(None, description="Sent to contact")
    expected_payment_date: Optional[date] = Field(None, description="Expected payment date")
    planned_payment_date: Optional[date] = Field(None, description="Planned payment date")
    updated_date_utc: Optional[datetime] = Field(None, description="Last updated")
    
    class Config:
        use_enum_values = True


class XeroOrganization(BaseModel):
    """Model for Xero organization."""
    
    organisation_id: Optional[str] = Field(None, description="Organisation ID")
    api_key: Optional[str] = Field(None, description="API key")
    name: Optional[str] = Field(None, description="Organisation name")
    legal_name: Optional[str] = Field(None, description="Legal name")
    pays_tax: Optional[bool] = Field(None, description="Pays tax")
    version: Optional[str] = Field(None, description="Version")
    organisation_type: Optional[str] = Field(None, description="Organisation type")
    base_currency: Optional[str] = Field(None, description="Base currency")
    country_code: Optional[str] = Field(None, description="Country code")
    is_demo_company: Optional[bool] = Field(None, description="Is demo company")
    organisation_status: Optional[str] = Field(None, description="Organisation status")
    registration_number: Optional[str] = Field(None, description="Registration number")
    employer_identification_number: Optional[str] = Field(None, description="EIN")
    tax_number: Optional[str] = Field(None, description="Tax number")
    financial_year_end_day: Optional[int] = Field(None, description="Financial year end day")
    financial_year_end_month: Optional[int] = Field(None, description="Financial year end month")
    sales_tax_basis: Optional[str] = Field(None, description="Sales tax basis")
    sales_tax_period: Optional[str] = Field(None, description="Sales tax period")
    default_sales_tax: Optional[str] = Field(None, description="Default sales tax")
    default_purchases_tax: Optional[str] = Field(None, description="Default purchases tax")
    period_lock_date: Optional[date] = Field(None, description="Period lock date")
    end_of_year_lock_date: Optional[date] = Field(None, description="End of year lock date")
    created_date_utc: Optional[datetime] = Field(None, description="Created date")
    timezone: Optional[str] = Field(None, description="Timezone")
    organisation_entity_type: Optional[str] = Field(None, description="Entity type")
    short_code: Optional[str] = Field(None, description="Short code")
    line_of_business: Optional[str] = Field(None, description="Line of business")
    addresses: Optional[List[XeroAddress]] = Field(None, description="Addresses")
    phones: Optional[List[XeroPhone]] = Field(None, description="Phone numbers")
    external_links: Optional[List[Dict[str, Any]]] = Field(None, description="External links")


class XeroTenant(BaseModel):
    """Model for Xero tenant information."""
    
    id: str = Field(..., description="Tenant ID")
    auth_event_id: str = Field(..., description="Auth event ID")
    tenant_id: str = Field(..., description="Tenant ID")
    tenant_type: str = Field(..., description="Tenant type")
    tenant_name: Optional[str] = Field(None, description="Tenant name")
    created_date_utc: Optional[datetime] = Field(None, description="Created date")
    updated_date_utc: Optional[datetime] = Field(None, description="Updated date")
    organisations: Optional[List[XeroOrganization]] = Field(None, description="Organizations")
