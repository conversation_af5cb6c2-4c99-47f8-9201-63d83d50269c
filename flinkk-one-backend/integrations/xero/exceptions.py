"""
Xero Integration Custom Exceptions

Defines custom exceptions for the Xero integration to provide
better error handling and debugging capabilities.
"""

from typing import Optional, Dict, Any


class XeroIntegrationError(Exception):
    """Base exception for Xero integration errors."""
    
    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class XeroAuthenticationError(XeroIntegrationError):
    """Raised when Xero authentication fails."""
    pass


class XeroTokenError(XeroIntegrationError):
    """Raised when there are issues with Xero tokens."""
    pass


class XeroAPIError(XeroIntegrationError):
    """Raised when Xero API calls fail."""
    
    def __init__(
        self,
        message: str,
        status_code: Optional[int] = None,
        response_data: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        self.status_code = status_code
        self.response_data = response_data or {}
        super().__init__(message, **kwargs)


class XeroConfigurationError(XeroIntegrationError):
    """Raised when Xero configuration is invalid."""
    pass


class XeroTenantError(XeroIntegrationError):
    """Raised when there are issues with Xero tenant operations."""
    pass


class XeroRateLimitError(XeroAPIError):
    """Raised when Xero API rate limits are exceeded."""
    
    def __init__(
        self,
        message: str = "Xero API rate limit exceeded",
        retry_after: Optional[int] = None,
        **kwargs
    ):
        self.retry_after = retry_after
        super().__init__(message, **kwargs)
